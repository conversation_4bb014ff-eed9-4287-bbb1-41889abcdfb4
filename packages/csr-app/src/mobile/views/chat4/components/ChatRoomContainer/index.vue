<template>
  <div class="chat-room-container" @click="handleContainerClick">
    <!-- 增强背景组件 -->
    <EnhancedBackground
      :background-video="backgroundVideo"
      :background-image="backgroundImage"
      :default-image="defaultImage"
      :animated-images="animatedImages"
      :transition-mode="'fade'"
      class="background-container"
      @resource-loading="handleResourceLoading"
      @transition-complete="handleTransitionComplete"
      @animated-image-complete="handleAnimatedImageComplete"
      @unlock-blur="handleUnlockBlur"
    />

    <!-- 通用头部组件 -->
    <CommonHeader
      :character-name="characterName"
      :character-avatar="characterImage"
      :show-viewer-count="false"
      @back-click="handleBackClick"
    >
      <template #back-button>
        <slot name="back-button">
          <span class="back-icon"><icon-left /></span>
        </slot>
      </template>
      <template #coin-display>
        <slot name="coin-display"></slot>
      </template>
    </CommonHeader>

    <!-- 聊天消息区域 -->
    <div class="chat-messages" :class="{ expanded: isInputExpanded }">
      <slot name="chat-interface"></slot>

      <!-- 输入状态提示 -->
      <div v-if="showTypingIndicator" class="typing-indicator">
        <div class="typing-content">
          <div class="typing-avatar">
            <img :src="characterImage" :alt="characterName" />
          </div>
          <div class="typing-bubble">
            <div class="typing-text">{{ characterName }} is typing</div>
            <div class="typing-dots">
              <span class="dot"></span>
              <span class="dot"></span>
              <span class="dot"></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 礼物动画效果 -->
    <div class="gift-animation-container">
      <LiveGiftAnimation ref="giftAnimationRef" />
    </div>

    <!-- Stage 好感度系统插槽 -->
    <div class="stage-container">
      <slot name="stage" />
    </div>

    <!-- 新的底部输入组件 -->
    <ChatBottomInput
      ref="chatBottomInputRef"
      :disabled="isLoading"
      @send-message="handleSendMessage"
      @feature-click="handleFeatureClick"
      @input-focus="handleInputFocus"
      @input-blur="handleInputBlur"
      @expand-change="handleExpandChange"
      @click.stop
    />

    <!-- 场景解锁弹窗 -->
    <SceneUnlockModal
      :visible="showUnlockModal"
      :scene-name="lockedSceneName"
      :required-level="lockedSceneRequiredLevel"
      :required-coins="lockedSceneRequiredCoins"
      :required-heart-value="lockedSceneRequiredHeartValue"
      :current-level="currentFavorabilityLevel"
      :current-heart-value="currentHeartValue"
      @close="handleUnlockModalClose"
      @boost-favorability="handleBoostFavorability"
    />

    <!-- 解锁提示 Toast -->
    <div class="toast-container">
      <Chat4Toast
        :visible="toastState.visible"
        :message="toastState.message"
        :type="toastState.type"
        :duration="toastState.duration"
        @close="hideToast"
      />
    </div>

    <!-- 场景付费确认弹窗 -->
    <ScenePaymentModal
      :visible="showPaymentModal"
      :scene-name="paymentSceneName"
      :coins-required="paymentCoinsRequired"
      :current-coins="userStore.userInfo?.coins || 0"
      @confirm="handlePaymentConfirm"
      @cancel="handlePaymentCancel"
      @close="handlePaymentCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useChatEventsStore } from '@/store/chat-events'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useUserStore } from '@/store/user'
import { useStoryStore } from '@/store/story'
import { useChat4Store } from '@/store/chat4'
import { useSceneEvents } from '@/composables/useSceneEvents'
import { type SceneCustomEventDetail, Chat4SceneId } from '@/types/chat4-scene'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { Message } from '@/mobile/components/Message'
import CommonHeader from '../CommonHeader/index.vue'
import EnhancedBackground from '@/mobile/views/chat2/components/ChatBackground/EnhancedBackground.vue'
import SceneUnlockModal from '../SceneUnlockModal/index.vue'
import LiveGiftAnimation from '../LiveGiftAnimation/index.vue'
import ChatBottomInput from '../ChatBottomInput/index.vue'
import Chat4Toast from '../Chat4Toast/index.vue'
import ScenePaymentModal from '../ScenePaymentModal/index.vue'
import type { Present } from '@/api/chat-multivariate'
import { useSceneUnlock, SCENE_ID_MAP } from '../../composables/useSceneUnlock'
import { useChat4Toast } from '../../composables/useChat4Toast'
import { useScenePayment } from '../../composables/useScenePayment'
import { SceneUnlockUtils } from '@/types/favorability'
import { useLocalStorage } from '@vueuse/core'
import { STORAGE_KEYS } from '@/utils/local-storage'
import { buyHeartValue } from '@/api/game'

interface Props {
  characterImage?: string
  characterName?: string
  backgroundImage?: string
  backgroundVideo?: string
  defaultImage?: string
  animatedImages?: string[]
  currentScene?: string // 添加当前场景ID
}

const props = withDefaults(defineProps<Props>(), {
  characterName: 'Character',
  backgroundImage: 'https://cdn.magiclight.ai/assets/playshot/chat-bg.jpg',
  backgroundVideo: '',
  defaultImage: 'https://cdn.magiclight.ai/assets/playshot/chat-bg.jpg',
  animatedImages: () => [],
})

// 事件定义
const emit = defineEmits<{
  messageSent: [message: string]
  inputFocus: []
  inputBlur: []
  sceneEvent: [eventName: string, detail: SceneCustomEventDetail]
  navClick: [navType: string]
  backToLive: []
  backClick: []
}>()

// Store引用
const chatEventsStore = useChatEventsStore()
const chatMessagesStore = useChatMessagesStore()
const userStore = useUserStore()
const storyStore = useStoryStore()
const chat4Store = useChat4Store()

// 场景解锁状态管理
const sceneUnlock = useSceneUnlock()
const { isSceneUnlocked, getSceneRequiredLevelText } = sceneUnlock

// 场景付费管理（chat4使用）
const scenePayment = useScenePayment()
const { sceneButtonStates, purchaseSceneAccess } = scenePayment

// Toast 提示管理
const { toastState, hideToast, showUnlockToast } = useChat4Toast()

// 付费确认弹窗状态
const showPaymentModal = ref(false)
const paymentSceneName = ref('')
const paymentCoinsRequired = ref(0)
const paymentSceneId = ref('')
const paymentResolve = ref<((value: boolean) => void) | null>(null)

// 解锁弹窗状态
const showUnlockModal = ref(false)
const lockedSceneName = ref('')
const lockedSceneRequiredLevel = ref('')
const lockedSceneRequiredCoins = ref(0)
const lockedSceneRequiredHeartValue = ref(0)

// 输入框展开状态
const isInputExpanded = ref(false)

// 当前用户状态 - 从实际的好感度系统获取
const currentFavorabilityLevel = computed(() => {
  const favorabilityState = chatEventsStore.favorabilityState
  if (!favorabilityState) return 'Lv0'

  const currentLevel = favorabilityState.currentLevel
  // 使用SceneUnlockUtils格式化等级显示，level0 显示为 Lv0
  return currentLevel ? SceneUnlockUtils.formatLevelText(currentLevel) : 'Lv0'
})

const currentHeartValue = computed(() => {
  const favorabilityState = chatEventsStore.favorabilityState
  return favorabilityState?.currentHeartValue || 0
})

// 响应式数据
const isLoading = ref(false)
const isFocused = ref(false)
const showTypingIndicator = ref(false)

// 组件引用
const giftAnimationRef = ref<InstanceType<typeof LiveGiftAnimation> | null>(
  null,
)
const chatBottomInputRef = ref<InstanceType<typeof ChatBottomInput> | null>(
  null,
)

// 场景事件监听器
useSceneEvents({
  // 好友请求事件处理器
  onFriendRequest: (detail: SceneCustomEventDetail) => {
    console.log('chat4-chatroom-events: Friend request received', detail)
    emit('sceneEvent', 'friendRequest', detail)
  },

  // 聊天场景变化处理器
  onChatSceneChange: (detail: SceneCustomEventDetail) => {
    console.log('chat4-chatroom-events: Chat scene changed', detail)
    emit('sceneEvent', 'chatSceneChange', detail)
  },

  // 默认场景变化处理器
  onDefaultSceneChange: (detail: SceneCustomEventDetail) => {
    console.log('chat4-chatroom-events: Default scene changed', detail)
    emit('sceneEvent', 'defaultSceneChange', detail)
  },

  // 通用场景事件处理器
  onSceneEvent: (eventName: string, detail: SceneCustomEventDetail) => {
    console.log(
      `chat4-chatroom-events: Scene event received: ${eventName}`,
      detail,
    )
  },
})

// 消息发送处理 (从新的底部输入组件接收消息)
const handleSendMessage = async (message: string) => {
  console.log('Sending message:', message)
  if (!message || isLoading.value) return

  try {
    isLoading.value = true

    // 清理输入状态
    hideCharacterTyping()

    // 触发父组件事件，让父组件处理实际的发送逻辑
    emit('messageSent', message)

    console.log('Message sent to parent component:', message)
  } catch (error) {
    console.error('Failed to send message:', error)
    // 可以在这里添加错误提示
  } finally {
    isLoading.value = false
  }
}

// 返回按钮点击处理
const handleBackClick = () => {
  emit('backClick')
}

// 输入框焦点处理
const handleInputFocus = () => {
  isFocused.value = true
  emit('inputFocus')
}

const handleInputBlur = () => {
  isFocused.value = false
  emit('inputBlur')

  // 失焦时隐藏角色输入提示
  hideCharacterTyping()
}

// 显示角色正在输入的提示
const showCharacterTyping = () => {
  showTypingIndicator.value = true
}

// 隐藏角色输入提示
const hideCharacterTyping = () => {
  showTypingIndicator.value = false
}

// 新的底部输入组件事件处理
const handleFeatureClick = (feature: string) => {
  console.log('Feature clicked:', feature)

  // 特殊处理meetup：需要检查Meetup场景的解锁条件，但跳转到Map场景
  if (feature === 'meetup') {
    // 检查Meetup场景的好感度要求
    const favorabilityState = chatEventsStore.favorabilityState

    if (favorabilityState && favorabilityState.sceneConditions) {
      const { sceneConditions, currentHeartValue } = favorabilityState

      const meetupCondition = sceneConditions.find(
        (c: any) => c.scene_id === 'Meetup' && c.requirement === 'heart_value',
      )

      if (meetupCondition && currentHeartValue < meetupCondition.value) {
        // Meetup 场景好感度不足，显示解锁弹窗
        console.log(
          `Meetup scene requires heart value ${meetupCondition.value}, current: ${currentHeartValue}`,
        )
        const sceneStatus = sceneUnlock.getSceneUnlockStatus('Meetup')

        showUnlockModal.value = true
        lockedSceneName.value = 'Meetup'
        lockedSceneRequiredLevel.value =
          getSceneRequiredLevelText('Meetup') || ''
        lockedSceneRequiredCoins.value = sceneStatus.requiredCoins || 0
        lockedSceneRequiredHeartValue.value =
          meetupCondition.value - currentHeartValue
        return
      }
    }

    // 如果Meetup解锁检查通过，跳转到Map场景
    handleNavClick('Map')
    return
  }

  // 其他场景直接使用SCENE_ID_MAP映射
  const sceneId = SCENE_ID_MAP[feature as keyof typeof SCENE_ID_MAP] || feature
  handleNavClick(sceneId)
}

const handleExpandChange = (expanded: boolean) => {
  console.log('Bottom input expanded:', expanded)
  isInputExpanded.value = expanded
}

// 处理容器点击事件 - 点击外部区域收起ChatBottomInput
const handleContainerClick = () => {
  // 收起ChatBottomInput的功能面板
  chatBottomInputRef.value?.collapseFeatures()
}

// 显示付费确认弹窗
const showPaymentConfirmDialog = (
  sceneName: string,
  coinsRequired: number,
  sceneId: string,
): Promise<boolean> => {
  return new Promise((resolve) => {
    const sceneDisplayName = getSceneDisplayName(sceneName)

    paymentSceneName.value = sceneDisplayName
    paymentCoinsRequired.value = coinsRequired
    paymentSceneId.value = sceneId
    paymentResolve.value = resolve
    showPaymentModal.value = true
  })
}

// 付费确认处理
const handlePaymentConfirm = async () => {
  console.log('Payment confirmed by user')

  showPaymentModal.value = false

  const coins = paymentCoinsRequired.value
  const storyId = storyStore.currentStory?.id
  const actorId = storyStore.currentActor?.id
  const sceneId = paymentSceneId.value

  // 检查必要参数
  if (!storyId || !actorId) {
    console.error('Missing story or actor ID for scene payment')
    resolvePayment(false)
    return
  }

  // 1. 调用buyHeartValue接口扣费
  console.log('Calling buyHeartValue API')
  const heartResponse = await buyHeartValue({
    story_id: storyId,
    actor_id: actorId,
    coins: coins,
  })

  // 检查支付结果
  if (heartResponse.code !== '0') {
    console.error('Scene payment failed:', heartResponse.message)
    resolvePayment(false)
    return
  }

  console.log(`Scene payment successful for ${paymentSceneName.value}`)

  // 2. 更新用户钻石余额
  updateUserCoins(heartResponse.data?.remaining_coins, coins)

  // 3. 更新好感度值
  updateHeartValue(heartResponse.data?.heart_value)

  // 4. 记录场景访问
  console.log('Recording scene access')
  const sceneSuccess = await purchaseSceneAccess(sceneId, false)

  resolvePayment(sceneSuccess)
}

// 辅助函数：处理支付结果回调
const resolvePayment = (success: boolean) => {
  if (paymentResolve.value) {
    paymentResolve.value(success)
    paymentResolve.value = null
  }
}

// 辅助函数：更新用户钻石余额
const updateUserCoins = (
  remainingCoins: number | undefined,
  spentCoins: number,
) => {
  if (!userStore.userInfo) return
  if (remainingCoins !== undefined) {
    userStore.userInfo.coins = remainingCoins
  } else {
    const currentCoins = userStore.userInfo?.coins || 0
    userStore.userInfo.coins = Math.max(0, currentCoins - spentCoins)
  }
}

// 辅助函数：更新好感度值
const updateHeartValue = (heartValue: number | undefined) => {
  if (heartValue === undefined) return

  const oldHeartValue = chatEventsStore.favorabilityState.currentHeartValue
  console.log(
    `Updating heart value from scene payment: ${oldHeartValue} -> ${heartValue}`,
  )

  // heart_value是绝对值，不是增量
  chatEventsStore.favorabilityState.currentHeartValue = heartValue
  chatEventsStore.updateFavorabilityLevelInfo()
}

// 付费取消处理
const handlePaymentCancel = () => {
  console.log('Payment cancelled by user')

  showPaymentModal.value = false
  if (paymentResolve.value) {
    paymentResolve.value(false)
    paymentResolve.value = null
  }
}

// 导航点击处理
const handleNavClick = async (navType: string) => {
  console.log('Navigation clicked:', navType)

  // 上报导航点击事件
  reportEvent(ReportEvent.ClickMenu, {
    userId: userStore.userInfo?.uuid,
    actorId: storyStore.currentActor?.id,
    storyId: storyStore.currentStory?.id,
    menuType: navType,
  })

  // 检查场景是否解锁（除了 live 场景）
  if (navType !== Chat4SceneId.LIVING) {
    const sceneId =
      SCENE_ID_MAP[navType as keyof typeof SCENE_ID_MAP] || navType

    // Chat4 动态检查好感度场景的解锁要求
    const favorabilityState = chatEventsStore.favorabilityState

    if (favorabilityState && favorabilityState.sceneConditions) {
      const { sceneConditions, currentHeartValue } = favorabilityState

      // 查找当前场景的好感度条件
      const heartValueCondition = sceneConditions.find(
        (c: any) => c.scene_id === sceneId && c.requirement === 'heart_value',
      )

      if (
        heartValueCondition &&
        currentHeartValue < heartValueCondition.value
      ) {
        // 好感度场景解锁条件不足，显示解锁弹窗
        console.log(
          `Scene ${navType} requires heart value ${heartValueCondition.value}, current: ${currentHeartValue}`,
        )
        const sceneStatus = sceneUnlock.getSceneUnlockStatus(sceneId)

        showUnlockModal.value = true
        lockedSceneName.value = navType
        lockedSceneRequiredLevel.value =
          getSceneRequiredLevelText(sceneId) || ''
        lockedSceneRequiredCoins.value = sceneStatus.requiredCoins || 0
        lockedSceneRequiredHeartValue.value =
          heartValueCondition.value - currentHeartValue
        return
      }
    }

    // 其他场景的常规解锁检查
    const isUnlocked = isSceneUnlocked(sceneId)

    if (!isUnlocked) {
      // 其他场景的常规解锁检查
      console.log(`Scene ${navType} is locked, showing unlock modal`)
      const sceneStatus = sceneUnlock.getSceneUnlockStatus(sceneId)

      showUnlockModal.value = true
      lockedSceneName.value = navType
      lockedSceneRequiredLevel.value = getSceneRequiredLevelText(sceneId) || ''
      lockedSceneRequiredCoins.value = sceneStatus.requiredCoins || 0
      lockedSceneRequiredHeartValue.value = sceneStatus.requiredHeartValue || 0
      return
    }

    // Chat4 角色的付费检查（场景解锁后）
    if (navType !== Chat4SceneId.LIVING) {
      const buttonState = sceneButtonStates.value[sceneId]
      if (buttonState?.requiresPayment) {
        // 需要付费（不是第一次访问）
        console.log(`Scene ${navType} requires payment for chat4`)

        // 获取真实的钻石数量要求（从favorabilityState.sceneConditions获取）
        const favorabilityState = chatEventsStore.favorabilityState
        let actualCoinsRequired = buttonState.coinsRequired || 0

        if (favorabilityState && favorabilityState.sceneConditions) {
          const coinsCondition = favorabilityState.sceneConditions.find(
            (c: any) => c.scene_id === sceneId && c.requirement === 'coins',
          )
          if (coinsCondition) {
            actualCoinsRequired = coinsCondition.value || 0
          }
        }

        const userCoins = userStore.userInfo?.coins || 0

        // 如果需要0钻石，直接跳转
        if (actualCoinsRequired === 0) {
          console.log(`Scene ${navType} requires 0 coins, proceeding directly`)
          const recordSuccess = await purchaseSceneAccess(sceneId, true)
          if (!recordSuccess) {
            console.error('Failed to record access')
            // 记录失败也允许继续进入，不影响用户体验
          }
        } else {
          // 检查用户硬币是否足够
          if (userCoins < actualCoinsRequired) {
            console.log('Insufficient coins, showing toast')
            showUnlockToast(
              `需要 ${actualCoinsRequired} 硬币访问此场景，请先充值`,
            )
            return
          }

          // 显示付费确认弹窗
          console.log('Showing payment confirmation dialog')
          const confirmPayment = await showPaymentConfirmDialog(
            navType,
            actualCoinsRequired,
            sceneId,
          )

          if (!confirmPayment) {
            console.log('Payment cancelled by user')
            return
          }

          // 执行付费购买
          console.log('Executing payment purchase')
          const paymentSuccess = await purchaseSceneAccess(sceneId, false)

          if (!paymentSuccess) {
            console.log('Payment purchase failed')
            return
          }

          console.log(`Payment successful for scene ${navType}`)
        }
      } else {
        // 第一次访问，免费但需要记录
        console.log(`First access to scene ${navType}, recording visit`)
        const recordSuccess = await purchaseSceneAccess(sceneId, true)
        if (!recordSuccess) {
          console.error('Failed to record first access')
          // 记录失败也允许继续进入，不影响用户体验
        }
      }
    }
  }

  // 根据导航类型执行不同的处理逻辑
  // 特殊处理：tip场景打开礼物弹窗
  if (navType === 'Tip') {
    console.log('Opening gift modal from tip navigation')
    emit('navClick', navType)
    return
  }

  // 其他场景统一交给父组件处理
  console.log('Navigating to scene:', navType)

  // 触发父组件事件
  emit('navClick', navType)
}

// 播放礼物动画
const playGiftAnimation = (
  gift: Present,
  senderName: string = 'You',
  quantity: number = 1,
) => {
  if (giftAnimationRef.value) {
    giftAnimationRef.value.playGiftAnimation(gift, senderName, quantity)
  }
}

// EnhancedBackground 事件处理方法
const handleResourceLoading = (loading: boolean) => {
  console.log('Background resource loading:', loading)
  // 可以在这里添加加载状态的处理逻辑
}

const handleTransitionComplete = () => {
  console.log('Background transition complete')
  // 可以在这里添加过渡完成后的处理逻辑
}

const handleAnimatedImageComplete = () => {
  console.log('Animated image sequence complete')
  // 可以在这里添加动画图片序列完成后的处理逻辑
}

// 解锁弹窗事件处理
const handleUnlockModalClose = () => {
  showUnlockModal.value = false
  lockedSceneName.value = ''
  lockedSceneRequiredLevel.value = ''
  lockedSceneRequiredCoins.value = 0
  lockedSceneRequiredHeartValue.value = 0
}

const handleBoostFavorability = () => {
  console.log('Boost favorability clicked')
  handleUnlockModalClose()
  // 可以触发打开礼物弹窗
  emit('navClick', Chat4SceneId.TIP)
}

// 解锁模糊处理
const handleUnlockBlur = (tag: string, requiredHeartValue: number) => {
  console.log('Unlock blur requested in ChatRoomContainer:', {
    tag,
    requiredHeartValue,
  })
  chat4Store.requestUnlockBlur(tag, requiredHeartValue)
}

// 监听聊天事件状态变化
watch(
  () => chatEventsStore.error,
  (error) => {
    if (error) {
      console.error('Chat events error:', error)
      isLoading.value = false
    }
  },
)

// 监听消息状态变化
watch(
  () => chatMessagesStore.isActorThinking,
  (thinking) => {
    if (thinking) {
      console.log('Actor is thinking...')
      // 显示角色正在输入的提示
      showCharacterTyping()
    } else {
      // 隐藏角色输入提示
      hideCharacterTyping()
    }
  },
)

// 跟踪场景解锁状态变化
const previousUnlockStatuses = ref<Map<string, boolean>>(new Map())

// 获取解锁提示记录的存储key
const getUnlockToastStorageKey = () => {
  const actorId = storyStore.currentActor?.id
  const storyId = storyStore.currentStory?.id
  return `${STORAGE_KEYS.CHAT4_UNLOCK_TOASTS}_${actorId}_${storyId}`
}

// 使用 VueUse 的 useLocalStorage 管理已提示的解锁场景
const shownUnlockToasts = useLocalStorage<string[]>(
  getUnlockToastStorageKey(),
  [],
)

// 检查场景是否已提示过
const hasShownUnlockToast = (sceneId: string): boolean => {
  return shownUnlockToasts.value.includes(sceneId)
}

// 记录已提示的解锁场景
const markUnlockToastAsShown = (sceneId: string) => {
  if (!hasShownUnlockToast(sceneId)) {
    shownUnlockToasts.value.push(sceneId)
  }
}

// 场景解锁状态监听
const handleSceneUnlockStatusUpdate = (event: CustomEvent) => {
  console.log('Scene unlock status updated:', event.detail)

  // 获取所有场景的当前解锁状态
  const allSceneIds = [
    'Video',
    'Monitor',
    'Meetup',
    'Dancing',
    'Concert',
    'Tip',
    'Moment',
    'Diary',
  ]

  allSceneIds.forEach((sceneId) => {
    const wasLocked = previousUnlockStatuses.value.get(sceneId) === false
    const isNowUnlocked = isSceneUnlocked(sceneId)

    // 如果场景从锁定变为解锁，且之前没有提示过，显示解锁提示
    if (wasLocked && isNowUnlocked && !hasShownUnlockToast(sceneId)) {
      console.log(`Scene ${sceneId} just unlocked!`)
      const sceneDisplayName = getSceneDisplayName(sceneId)
      showUnlockToast(`${sceneDisplayName} unlocked!`)
      markUnlockToastAsShown(sceneId)
    }

    // 更新状态记录
    previousUnlockStatuses.value.set(sceneId, isNowUnlocked)
  })
}

// 获取场景显示名称的辅助函数
const getSceneDisplayName = (sceneId: string): string => {
  const sceneNameMap: { [key: string]: string } = {
    Video: 'Video Call',
    Monitor: 'Monitor',
    Meetup: 'Meet up',
    Dancing: 'Dancing',
    Concert: 'Concert',
    Tip: 'Gift',
    Moment: 'Moments',
  }
  return sceneNameMap[sceneId] || sceneId
}

// 组件挂载时的初始化
onMounted(() => {
  console.log('ChatRoomContainer mounted, connecting to chat events')

  // 如果需要，可以在这里进行额外的初始化
  if (userStore.isGuest) {
    console.log('User is guest, some features may be limited')
  }

  // 初始化场景解锁状态记录
  const allSceneIds = [
    'Video',
    'Monitor',
    'Meetup',
    'Dancing',
    'Concert',
    'Tip',
    'Moment',
    'Diary',
  ]
  allSceneIds.forEach((sceneId) => {
    previousUnlockStatuses.value.set(sceneId, isSceneUnlocked(sceneId))
  })

  // 监听场景解锁状态更新事件
  window.addEventListener(
    'sceneUnlockStatusUpdated',
    handleSceneUnlockStatusUpdate as any,
  )
})

onUnmounted(() => {
  console.log('ChatRoomContainer unmounted')

  // 清理所有计时器
  hideCharacterTyping()

  // 清理场景解锁事件监听器
  window.removeEventListener(
    'sceneUnlockStatusUpdated',
    handleSceneUnlockStatusUpdate as any,
  )
})

// 测试函数 - 手动触发解锁 Toast（用于调试）
const testUnlockToast = (sceneId = 'Video') => {
  if (!hasShownUnlockToast(sceneId)) {
    const sceneDisplayName = getSceneDisplayName(sceneId)
    showUnlockToast(`${sceneDisplayName} unlocked!`)
    markUnlockToastAsShown(sceneId)
  } else {
    console.log(`Scene ${sceneId} unlock toast already shown before`)
  }
}

// 清除解锁提示历史记录（用于重新开始游戏时调用）
const clearUnlockToastHistory = () => {
  shownUnlockToasts.value = []
  console.log('Unlock toast history cleared')
}

// 暴露方法给父组件
defineExpose({
  playGiftAnimation,
  testUnlockToast, // 暴露测试函数
  clearUnlockToastHistory, // 暴露清除历史记录的方法
})
</script>

<style lang="less" scoped>
.chat-room-container {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  max-width: 100vw;
  position: relative;
  overflow: hidden;
  background: #f6f6f6;
}

/* 背景容器 */
.background-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 通用头部组件已处理顶部区域样式 */

/* 聊天消息区域 */
.chat-messages {
  position: absolute;
  top: 200px; /* 头部高度 + 一些间距 */
  left: 15px;
  right: 15px;
  bottom: 100px; /* 为新的底部输入组件留出空间 */
  z-index: 3;
  overflow-y: auto;
  padding: 20px 0;
  transition: bottom 0.3s ease-out;
}

.chat-messages.expanded {
  bottom: 280px; /* 展开时增加底部间距，避免被功能区域遮挡 */
}

/* 输入状态提示 */
.typing-indicator {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  z-index: 5;
  animation: slideInUp 0.3s ease-out;
}

.typing-content {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  padding: 0 10px;
}

.typing-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.typing-bubble {
  background: rgba(76, 60, 89, 0.95);
  border-radius: 18px 18px 18px 4px;
  padding: 12px 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(218, 255, 150, 0.2);
  max-width: 200px;
}

.typing-text {
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Work Sans', sans-serif;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 4px;
}

.typing-dots {
  display: flex;
  gap: 4px;
  align-items: center;
}

.dot {
  width: 6px;
  height: 6px;
  background: #daff96;
  border-radius: 50%;
  animation: typingDot 1.4s infinite ease-in-out;

  &:nth-child(1) {
    animation-delay: 0s;
  }

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

@keyframes typingDot {
  0%,
  60%,
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 礼物动画容器 */
.gift-animation-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 20; /* 确保礼物动画在最前面 */
  pointer-events: none;
}

/* Stage 好感度系统区域 */
.stage-container {
  position: absolute;
  top: 90px; /* 与聊天消息区域对齐 */
  right: 15px; /* 右侧边距 */
  z-index: 4; /* 在聊天消息之上 */
  pointer-events: auto; /* 确保可以点击 */
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Toast 容器样式 */
.toast-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1001; /* 确保在其他元素之上 */
  pointer-events: none; /* 允许点击穿透 */
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .toast-container {
    /* 桌面端可以保持相同的居中位置 */
  }
}
</style>
