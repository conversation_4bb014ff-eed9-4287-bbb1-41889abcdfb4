<template>
  <div
    v-if="showOptions"
    class="chat4-options"
    :class="{ 'horizontal-layout': isHorizontalLayout }"
  >
    <button
      v-for="(option, index) in options"
      :key="option.option_id"
      class="chat4-option-button"
      :class="{
        'unlocked': option.is_purchased,
        'first-option': index === 0,
        'second-option': index === 1,
        'third-option': index === 2,
        'highlight-animation': option.is_highlight,
      }"
      @click="handleSelect(option)"
    >
      <span v-html="option.text"></span>
      <div
        class="coins-container"
        v-if="option.paid_required && !option.is_purchased"
      >
        <img
          src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
          alt="coins"
        />
        <span class="coins-text">{{ option?.coins || 0 }}</span>
      </div>
      <div
        class="unlock-container"
        v-if="option.paid_required && option.is_purchased"
      >
        <icon-unlock />
      </div>
    </button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { IconUnlock } from '@arco-design/web-vue/es/icon'
import { useChatResourcesStore } from '@/store/chat-resources'

interface ChatOption {
  option_id: string
  text: string
  paid_required: boolean
  is_purchased?: boolean
  coins?: number
  is_highlight?: boolean
}

const emit = defineEmits<{
  (e: 'select', option: ChatOption): void
}>()

const chatResourcesStore = useChatResourcesStore()

const showOptions = computed(() => {
  return chatResourcesStore.chat4Card?.length
})

const options = computed(() => chatResourcesStore.chat4Card || [])

const handleSelect = (option: ChatOption) => {
  emit('select', option)
}

const isHorizontalLayout = computed(() => {
  return options.value.length <= 2
})
</script>

<style lang="less" scoped>
.chat4-options {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 335px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0 20px;
  z-index: 11;

  &.horizontal-layout {
    flex-direction: row;
    justify-content: center;
    gap: 16px;

    .chat4-option-button {
      flex: 1;
      min-width: 0;
      min-height: 36px;
    }
  }

  .chat4-option-button {
    position: relative;
    padding: 6px 13px;
    border-radius: 0;
    background: linear-gradient(
      90deg,
      rgba(31, 0, 56, 0) 0%,
      rgba(31, 0, 56, 1) 50%,
      rgba(31, 0, 56, 0) 100%
    );
    border: none;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
    color: #ffffff;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
    transition: all 0.2s ease;
    text-align: center;
    overflow: hidden;
    height: 36px;

    // 添加上下边框的渐变效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 1) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      z-index: 0;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 1) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      z-index: 0;
    }

    // 确保内容在背景之上
    span,
    .coins-container,
    .unlock-container {
      position: relative;
      z-index: 1;
    }

    &:hover {
      background: linear-gradient(
        90deg,
        rgba(31, 0, 56, 0.2) 0%,
        rgba(31, 0, 56, 1) 50%,
        rgba(31, 0, 56, 0.2) 100%
      );
      filter: brightness(1.1);
    }

    &:active {
      filter: brightness(0.9);
    }

    span {
      line-height: 1.4;
    }

    &.unlocked {
      position: relative;
      &::before {
        display: inline-block;
        content: 'Unlocked';
        position: absolute;
        top: 0;
        left: 0;
        color: #1f0038;
        font-size: 10px;
        font-weight: 500;
        border-radius: 0px 0px 8px 0px;
        background: #daff96;
        padding: 3px 6px 2px 8px;
        z-index: 2;
      }
    }

    .coins-container {
      position: absolute;
      right: -6px;
      top: -6px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      border-radius: 34px;
      border: 1px solid #daff96;
      padding: 2px 10px 2px 7px;
      background: #1f0038;
      height: 20px;

      img {
        width: 13px;
        height: 13px;
      }

      .coins-text {
        color: #daff96;
        font-size: 11px;
        font-weight: 600;
      }
    }

    .unlock-container {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      color: rgba(255, 255, 255, 0.4);
    }

    // Add highlight animation styles
    &.highlight-animation {
      position: relative;
      overflow: visible;

      // 动画边框
      &::before {
        animation: borderGlow 1.5s linear infinite;
      }

      &::after {
        animation: borderGlow 1.5s linear infinite;
      }
    }
  }
}

@keyframes borderGlow {
  0% {
    background: linear-gradient(
      90deg,
      rgba(255, 215, 0, 0) 0%,
      rgba(255, 215, 0, 1) 50%,
      rgba(255, 215, 0, 0) 100%
    );
  }
  50% {
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 1) 50%,
      rgba(255, 255, 255, 0) 100%
    );
  }
  100% {
    background: linear-gradient(
      90deg,
      rgba(255, 215, 0, 0) 0%,
      rgba(255, 215, 0, 1) 50%,
      rgba(255, 215, 0, 0) 100%
    );
  }
}
</style>
